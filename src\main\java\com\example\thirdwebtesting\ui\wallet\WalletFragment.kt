package com.example.thirdwebtesting.ui.wallet

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.example.thirdwebtesting.databinding.FragmentWalletBinding

class WalletFragment : Fragment() {

    private var _binding: FragmentWalletBinding? = null
    private val binding get() = _binding!!

    private lateinit var walletViewModel: WalletViewModel

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        walletViewModel = ViewModelProvider(this)[WalletViewModel::class.java]

        _binding = FragmentWalletBinding.inflate(inflater, container, false)
        val root: View = binding.root

        setupUI()
        observeAppKitState()

        return root
    }

    private fun setupUI() {
        // Connect button - for now shows a demo message
        binding.btnConnectWallet.setOnClickListener {
            Toast.makeText(requireContext(), "Reown AppKit integration coming soon! Please add your Project ID from https://cloud.reown.com", Toast.LENGTH_LONG).show()
            // TODO: Implement actual AppKit modal opening
            // This will be: Modal.open(Modal.Model.ConnectWallet)
        }

        // Disconnect button
        binding.btnDisconnect.setOnClickListener {
            Toast.makeText(requireContext(), "Disconnect functionality will be available once AppKit is properly integrated", Toast.LENGTH_SHORT).show()
            // TODO: Implement actual disconnect
            // This will be: AppKit.disconnect()
        }
    }

    private fun observeAppKitState() {
        // Observe AppKit session state
        AppKit.getSessionState().observe(viewLifecycleOwner) { sessionState ->
            when (sessionState) {
                is Session.State.Connected -> {
                    updateUIForConnectionState(true)
                    val session = sessionState.session
                    binding.tvWalletAddress.text = "Address: ${session.accounts.firstOrNull()?.let {
                        "${it.take(6)}...${it.takeLast(4)}"
                    } ?: "Unknown"}"
                    binding.tvConnectionStatus.text = "Connected via ${session.peer.name}"
                }
                is Session.State.Disconnected -> {
                    updateUIForConnectionState(false)
                    binding.tvWalletAddress.text = "No wallet connected"
                    binding.tvConnectionStatus.text = "Disconnected"
                }
            }
        }
    }

    private fun updateUIForConnectionState(isConnected: Boolean) {
        if (isConnected) {
            // Hide connect button, show wallet info and disconnect button
            binding.btnConnectWallet.visibility = View.GONE
            binding.layoutWalletInfo.visibility = View.VISIBLE
            binding.btnDisconnect.visibility = View.VISIBLE
        } else {
            // Show connect button, hide wallet info and disconnect button
            binding.btnConnectWallet.visibility = View.VISIBLE
            binding.layoutWalletInfo.visibility = View.GONE
            binding.btnDisconnect.visibility = View.GONE
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
