package com.example.thirdwebtesting.ui.wallet

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.example.thirdwebtesting.databinding.FragmentWalletBinding
import com.reown.appkit.ui.openAppKit

class WalletFragment : Fragment() {

    private var _binding: FragmentWalletBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentWalletBinding.inflate(inflater, container, false)
        val root: View = binding.root

        setupUI()

        return root
    }

    private fun setupUI() {
        // Single button that opens the complete AppKit UI
        binding.btnConnectWallet.setOnClickListener {
            // This opens the full AppKit modal with all wallet options, UI, and functionality
            findNavController().openAppKit(
                shouldOpenChooseNetwork = false
            ) { error ->
                // Handle any errors if needed
                error?.let {
                    // Error handling is optional since AppKit handles most errors internally
                }
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
