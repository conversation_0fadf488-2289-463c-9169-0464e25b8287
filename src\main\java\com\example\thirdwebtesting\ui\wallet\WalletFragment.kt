package com.example.thirdwebtesting.ui.wallet

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import com.example.thirdwebtesting.databinding.FragmentWalletBinding

class WalletFragment : Fragment() {

    private var _binding: FragmentWalletBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentWalletBinding.inflate(inflater, container, false)
        val root: View = binding.root

        setupUI()

        return root
    }

    private fun setupUI() {
        // For now, show a message about AppKit integration
        binding.btnConnectWallet.setOnClickListener {
            Toast.makeText(
                requireContext(),
                "AppKit integration ready! Project ID: 7b44092789f3f1fd094a8bbec200372b",
                Toast.LENGTH_LONG
            ).show()

            // TODO: Add actual AppKit modal opening here
            // This will be: findNavController().openAppKit()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
