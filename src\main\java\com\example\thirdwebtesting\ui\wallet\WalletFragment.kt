package com.example.thirdwebtesting.ui.wallet

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import com.example.thirdwebtesting.databinding.FragmentWalletBinding
import com.reown.appkit.ui.openAppKit

class WalletFragment : Fragment() {

    private var _binding: FragmentWalletBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentWalletBinding.inflate(inflater, container, false)
        val root: View = binding.root

        setupUI()

        return root
    }

    private fun setupUI() {
        // Open AppKit's built-in wallet connection UI
        binding.btnConnectWallet.setOnClickListener {
            try {
                // This opens the complete AppKit modal with all wallet options and UI
                findNavController().openAppKit(
                    shouldOpenChooseNetwork = false
                ) { error ->
                    // AppKit handles most errors internally, but we can show custom messages if needed
                    error?.let {
                        Toast.makeText(
                            requireContext(),
                            "Error opening wallet modal: ${it.throwable?.message ?: "Unknown error"}",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            } catch (e: Exception) {
                Toast.makeText(
                    requireContext(),
                    "Error: ${e.message}",
                    Toast.LENGTH_SHORT
                ).show()
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
