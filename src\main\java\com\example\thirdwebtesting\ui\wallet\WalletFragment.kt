package com.example.thirdwebtesting.ui.wallet

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.example.thirdwebtesting.databinding.FragmentWalletBinding
import com.reown.appkit.client.AppKit
import com.reown.appkit.ui.openAppKit

class WalletFragment : Fragment() {

    private var _binding: FragmentWalletBinding? = null
    private val binding get() = _binding!!

    private lateinit var walletViewModel: WalletViewModel

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        walletViewModel = ViewModelProvider(this)[WalletViewModel::class.java]

        _binding = FragmentWalletBinding.inflate(inflater, container, false)
        val root: View = binding.root

        setupUI()
        observeAppKitState()

        return root
    }

    private fun setupUI() {
        // Connect button that opens AppKit modal
        binding.btnConnectWallet.setOnClickListener {
            try {
                findNavController().openAppKit(
                    shouldOpenChooseNetwork = false,
                    onError = { error ->
                        Toast.makeText(requireContext(), "Error opening wallet modal: ${error.throwable.message}", Toast.LENGTH_SHORT).show()
                    }
                )
            } catch (e: Exception) {
                Toast.makeText(requireContext(), "Error: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }

        // Disconnect button
        binding.btnDisconnect.setOnClickListener {
            try {
                AppKit.disconnect(
                    onSuccess = {
                        Toast.makeText(requireContext(), "Wallet disconnected", Toast.LENGTH_SHORT).show()
                    },
                    onError = { error ->
                        Toast.makeText(requireContext(), "Error disconnecting: ${error.throwable.message}", Toast.LENGTH_SHORT).show()
                    }
                )
            } catch (e: Exception) {
                Toast.makeText(requireContext(), "Error: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun observeAppKitState() {
        // Observe AppKit session state
        AppKit.getSessionState().observe(viewLifecycleOwner) { sessionState ->
            when (sessionState) {
                is AppKit.Model.SessionState.Connected -> {
                    updateUIForConnectionState(true)
                    val session = sessionState.session
                    binding.tvWalletAddress.text = "Address: ${session.accounts.firstOrNull()?.let {
                        "${it.take(6)}...${it.takeLast(4)}"
                    } ?: "Unknown"}"
                    binding.tvConnectionStatus.text = "Connected via ${session.peer.name}"
                }
                is AppKit.Model.SessionState.Disconnected -> {
                    updateUIForConnectionState(false)
                    binding.tvWalletAddress.text = "No wallet connected"
                    binding.tvConnectionStatus.text = "Disconnected"
                }
            }
        }
    }

    private fun updateUIForConnectionState(isConnected: Boolean) {
        if (isConnected) {
            // Hide connect button, show wallet info and disconnect button
            binding.btnConnectWallet.visibility = View.GONE
            binding.layoutWalletInfo.visibility = View.VISIBLE
            binding.btnDisconnect.visibility = View.VISIBLE
        } else {
            // Show connect button, hide wallet info and disconnect button
            binding.btnConnectWallet.visibility = View.VISIBLE
            binding.layoutWalletInfo.visibility = View.GONE
            binding.btnDisconnect.visibility = View.GONE
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
