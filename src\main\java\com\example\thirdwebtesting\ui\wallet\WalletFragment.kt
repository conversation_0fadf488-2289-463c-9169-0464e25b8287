package com.example.thirdwebtesting.ui.wallet

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.example.thirdwebtesting.databinding.FragmentWalletBinding
import com.example.thirdwebtesting.wallet.WalletManager

class WalletFragment : Fragment() {

    private var _binding: FragmentWalletBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var walletViewModel: WalletViewModel
    private lateinit var walletManager: WalletManager

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        walletViewModel = ViewModelProvider(this)[WalletViewModel::class.java]
        walletManager = WalletManager.getInstance(requireContext())

        _binding = FragmentWalletBinding.inflate(inflater, container, false)
        val root: View = binding.root

        setupUI()
        observeWalletState()

        return root
    }

    private fun setupUI() {
        // Connect buttons
        binding.btnConnectMetamask.setOnClickListener {
            walletManager.connectMetaMask()
        }

        binding.btnConnectWalletconnect.setOnClickListener {
            walletManager.connectWalletConnect()
        }

        binding.btnConnectThirdweb.setOnClickListener {
            walletManager.connectThirdwebEmbedded()
        }

        // Disconnect button
        binding.btnDisconnect.setOnClickListener {
            walletManager.disconnect()
        }

        // Refresh balance button
        binding.btnRefreshBalance.setOnClickListener {
            walletManager.refreshBalance()
        }
    }

    private fun observeWalletState() {
        // Observe connection status
        walletManager.isConnected.observe(viewLifecycleOwner) { isConnected ->
            updateUIForConnectionState(isConnected)
        }

        // Observe wallet address
        walletManager.walletAddress.observe(viewLifecycleOwner) { address ->
            binding.tvWalletAddress.text = if (address != null) {
                "Address: ${address.take(6)}...${address.takeLast(4)}"
            } else {
                "No wallet connected"
            }
        }

        // Observe wallet type
        walletManager.walletType.observe(viewLifecycleOwner) { type ->
            binding.tvWalletType.text = when (type) {
                WalletManager.WalletType.METAMASK -> "Connected via MetaMask"
                WalletManager.WalletType.WALLETCONNECT -> "Connected via WalletConnect"
                WalletManager.WalletType.THIRDWEB_EMBEDDED -> "Connected via thirdweb"
                else -> "No wallet connected"
            }
        }

        // Observe connection status messages
        walletManager.connectionStatus.observe(viewLifecycleOwner) { status ->
            binding.tvConnectionStatus.text = status
        }

        // Observe balance
        walletManager.balance.observe(viewLifecycleOwner) { balance ->
            binding.tvBalance.text = "Balance: $balance"
        }
    }

    private fun updateUIForConnectionState(isConnected: Boolean) {
        if (isConnected) {
            // Hide connection buttons, show wallet info and disconnect button
            binding.layoutConnectionButtons.visibility = View.GONE
            binding.layoutWalletInfo.visibility = View.VISIBLE
            binding.btnDisconnect.visibility = View.VISIBLE
            binding.btnRefreshBalance.visibility = View.VISIBLE
        } else {
            // Show connection buttons, hide wallet info and disconnect button
            binding.layoutConnectionButtons.visibility = View.VISIBLE
            binding.layoutWalletInfo.visibility = View.GONE
            binding.btnDisconnect.visibility = View.GONE
            binding.btnRefreshBalance.visibility = View.GONE
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
