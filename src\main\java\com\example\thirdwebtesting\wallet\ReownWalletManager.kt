package com.example.thirdwebtesting.wallet

import android.app.Application
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.net.Uri
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import kotlinx.coroutines.*
import org.web3j.crypto.ECKeyPair
import org.web3j.crypto.Keys
import java.security.SecureRandom

class ReownWalletManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "ReownWalletManager"
        private const val METAMASK_PACKAGE = "io.metamask"
        private const val PREFS_NAME = "reown_wallet_prefs"
        private const val KEY_WALLET_ADDRESS = "wallet_address"
        private const val KEY_WALLET_TYPE = "wallet_type"
        private const val KEY_PRIVATE_KEY = "private_key"
        
        // Reown AppKit configuration
        private const val PROJECT_ID = "YOUR_PROJECT_ID" // You'll need to get this from https://cloud.reown.com
        
        @Volatile
        private var INSTANCE: ReownWalletManager? = null
        
        fun getInstance(context: Context): ReownWalletManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ReownWalletManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    // Wallet connection state
    private val _isConnected = MutableLiveData<Boolean>(false)
    val isConnected: LiveData<Boolean> = _isConnected
    
    private val _walletAddress = MutableLiveData<String?>(null)
    val walletAddress: LiveData<String?> = _walletAddress
    
    private val _walletType = MutableLiveData<WalletType?>(null)
    val walletType: LiveData<WalletType?> = _walletType
    
    private val _connectionStatus = MutableLiveData<String>("Disconnected")
    val connectionStatus: LiveData<String> = _connectionStatus
    
    private val _balance = MutableLiveData<String>("0.0")
    val balance: LiveData<String> = _balance
    
    private val scope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    enum class WalletType {
        METAMASK,
        WALLETCONNECT,
        EMBEDDED_WALLET,
        UNKNOWN
    }
    
    init {
        // Initialize Reown AppKit
        initializeReownAppKit()
        // Load saved wallet state on initialization
        loadSavedWalletState()
    }
    
    private fun initializeReownAppKit() {
        try {
            // TODO: Initialize Reown AppKit here
            // This would typically involve setting up the AppKit client
            // For now, we'll use a simulated approach
            Log.d(TAG, "Reown AppKit initialized")
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing Reown AppKit", e)
        }
    }
    
    /**
     * Connect to MetaMask mobile app via deep linking
     */
    fun connectMetaMask() {
        try {
            _connectionStatus.value = "Connecting to MetaMask..."
            
            // Check if MetaMask is installed
            if (isMetaMaskInstalled()) {
                // Create deep link to MetaMask
                val intent = Intent(Intent.ACTION_VIEW).apply {
                    data = Uri.parse("metamask://")
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }
                context.startActivity(intent)
                
                // Simulate connection for demo purposes
                scope.launch {
                    delay(2000)
                    simulateSuccessfulConnection(WalletType.METAMASK, "******************************************")
                }
            } else {
                // Redirect to Play Store to install MetaMask
                redirectToMetaMaskInstall()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error connecting to MetaMask", e)
            _connectionStatus.value = "Failed to connect to MetaMask"
        }
    }
    
    /**
     * Connect via Reown AppKit (WalletConnect)
     */
    fun connectWalletConnect() {
        try {
            _connectionStatus.value = "Initializing WalletConnect..."
            
            // TODO: Use actual Reown AppKit connection here
            // For now, we'll simulate the connection
            scope.launch {
                delay(3000)
                simulateSuccessfulConnection(WalletType.WALLETCONNECT, "0x8ba1f109551bD432803012645Hac136c")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error connecting via WalletConnect", e)
            _connectionStatus.value = "Failed to connect via WalletConnect"
        }
    }
    
    /**
     * Create an embedded wallet
     */
    fun createEmbeddedWallet(email: String? = null) {
        try {
            _connectionStatus.value = "Creating embedded wallet..."
            
            scope.launch {
                try {
                    // Generate a new wallet using web3j
                    val keyPair = createNewWallet()
                    val address = "0x" + Keys.getAddress(keyPair)
                    
                    // Save wallet data
                    saveWalletData(address, WalletType.EMBEDDED_WALLET, keyPair.privateKey.toString(16))
                    
                    delay(1500) // Simulate processing time
                    simulateSuccessfulConnection(WalletType.EMBEDDED_WALLET, address)
                    
                    Log.d(TAG, "Created new embedded wallet: $address")
                } catch (e: Exception) {
                    Log.e(TAG, "Error creating embedded wallet", e)
                    _connectionStatus.value = "Failed to create embedded wallet"
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error creating embedded wallet", e)
            _connectionStatus.value = "Failed to create embedded wallet"
        }
    }
    
    /**
     * Disconnect the current wallet
     */
    fun disconnect() {
        try {
            _connectionStatus.value = "Disconnecting..."
            
            scope.launch {
                delay(1000)
                
                // Clear saved wallet data
                clearWalletData()
                
                _isConnected.value = false
                _walletAddress.value = null
                _walletType.value = null
                _balance.value = "0.0"
                _connectionStatus.value = "Disconnected"
                Log.d(TAG, "Wallet disconnected successfully")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error disconnecting wallet", e)
            _connectionStatus.value = "Error disconnecting"
        }
    }
    
    /**
     * Get wallet balance (simulated for demo)
     */
    fun refreshBalance() {
        if (_isConnected.value == true) {
            scope.launch {
                try {
                    _connectionStatus.value = "Refreshing balance..."
                    delay(1500)
                    
                    // Simulate fetching balance
                    val simulatedBalance = (Math.random() * 10).toString().take(6)
                    _balance.value = "$simulatedBalance ETH"
                    _connectionStatus.value = "Connected"
                } catch (e: Exception) {
                    Log.e(TAG, "Error refreshing balance", e)
                    _connectionStatus.value = "Error refreshing balance"
                }
            }
        }
    }
    
    private fun isMetaMaskInstalled(): Boolean {
        return try {
            context.packageManager.getPackageInfo(METAMASK_PACKAGE, 0)
            true
        } catch (e: Exception) {
            false
        }
    }
    
    private fun redirectToMetaMaskInstall() {
        try {
            val intent = Intent(Intent.ACTION_VIEW).apply {
                data = Uri.parse("https://play.google.com/store/apps/details?id=$METAMASK_PACKAGE")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)
            _connectionStatus.value = "Please install MetaMask and try again"
        } catch (e: Exception) {
            Log.e(TAG, "Error redirecting to MetaMask install", e)
            _connectionStatus.value = "Unable to open Play Store"
        }
    }
    
    private fun simulateSuccessfulConnection(type: WalletType, address: String) {
        _isConnected.value = true
        _walletAddress.value = address
        _walletType.value = type
        _connectionStatus.value = "Connected"
        
        // Save connection state
        saveWalletData(address, type)
        
        // Simulate getting balance
        refreshBalance()
        
        Log.d(TAG, "Successfully connected to ${type.name} wallet: $address")
    }
    
    private fun createNewWallet(): ECKeyPair {
        val random = SecureRandom()
        return Keys.createEcKeyPair(random)
    }
    
    private fun saveWalletData(address: String, type: WalletType, privateKey: String? = null) {
        prefs.edit().apply {
            putString(KEY_WALLET_ADDRESS, address)
            putString(KEY_WALLET_TYPE, type.name)
            privateKey?.let { putString(KEY_PRIVATE_KEY, it) }
            apply()
        }
    }
    
    private fun loadSavedWalletState() {
        val savedAddress = prefs.getString(KEY_WALLET_ADDRESS, null)
        val savedType = prefs.getString(KEY_WALLET_TYPE, null)
        
        if (savedAddress != null && savedType != null) {
            try {
                val walletType = WalletType.valueOf(savedType)
                _walletAddress.value = savedAddress
                _walletType.value = walletType
                _isConnected.value = true
                _connectionStatus.value = "Connected (Restored)"
                
                // Refresh balance for restored wallet
                refreshBalance()
                
                Log.d(TAG, "Restored wallet connection: $savedAddress ($walletType)")
            } catch (e: Exception) {
                Log.e(TAG, "Error restoring wallet state", e)
                clearWalletData()
            }
        }
    }
    
    private fun clearWalletData() {
        prefs.edit().clear().apply()
    }
    
    fun cleanup() {
        scope.cancel()
    }
}
