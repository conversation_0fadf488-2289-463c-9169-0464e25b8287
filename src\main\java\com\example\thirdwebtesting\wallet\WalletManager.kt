package com.example.thirdwebtesting.wallet

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import kotlinx.coroutines.*
import java.math.BigInteger

class WalletManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "WalletManager"
        private const val METAMASK_PACKAGE = "io.metamask"
        private const val WALLETCONNECT_BRIDGE = "https://bridge.walletconnect.org"
        
        @Volatile
        private var INSTANCE: WalletManager? = null
        
        fun getInstance(context: Context): WalletManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: WalletManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    // Wallet connection state
    private val _isConnected = MutableLiveData<Boolean>(false)
    val isConnected: LiveData<Boolean> = _isConnected
    
    private val _walletAddress = MutableLiveData<String?>(null)
    val walletAddress: LiveData<String?> = _walletAddress
    
    private val _walletType = MutableLiveData<WalletType?>(null)
    val walletType: LiveData<WalletType?> = _walletType
    
    private val _connectionStatus = MutableLiveData<String>("Disconnected")
    val connectionStatus: LiveData<String> = _connectionStatus
    
    private val _balance = MutableLiveData<String>("0.0")
    val balance: LiveData<String> = _balance
    
    private val scope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    enum class WalletType {
        METAMASK,
        WALLETCONNECT,
        THIRDWEB_EMBEDDED,
        UNKNOWN
    }
    
    /**
     * Connect to MetaMask mobile app via deep linking
     */
    fun connectMetaMask() {
        try {
            _connectionStatus.value = "Connecting to MetaMask..."
            
            // Check if MetaMask is installed
            if (isMetaMaskInstalled()) {
                // Create deep link to MetaMask
                val intent = Intent(Intent.ACTION_VIEW).apply {
                    data = Uri.parse("metamask://")
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK
                }
                context.startActivity(intent)
                
                // Simulate connection for demo purposes
                // In a real implementation, you would handle the callback from MetaMask
                scope.launch {
                    delay(2000)
                    simulateSuccessfulConnection(WalletType.METAMASK, "******************************************")
                }
            } else {
                // Redirect to Play Store to install MetaMask
                redirectToMetaMaskInstall()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error connecting to MetaMask", e)
            _connectionStatus.value = "Failed to connect to MetaMask"
        }
    }
    
    /**
     * Connect via WalletConnect protocol
     */
    fun connectWalletConnect() {
        try {
            _connectionStatus.value = "Initializing WalletConnect..."
            
            // In a real implementation, you would initialize WalletConnect here
            // For now, we'll simulate the connection
            scope.launch {
                delay(3000)
                simulateSuccessfulConnection(WalletType.WALLETCONNECT, "0x8ba1f109551bD432803012645Hac136c")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error connecting via WalletConnect", e)
            _connectionStatus.value = "Failed to connect via WalletConnect"
        }
    }
    
    /**
     * Connect using thirdweb embedded wallet
     */
    fun connectThirdwebEmbedded(email: String? = null) {
        try {
            _connectionStatus.value = "Creating thirdweb wallet..."
            
            // In a real implementation, you would use thirdweb SDK here
            // For now, we'll simulate the connection
            scope.launch {
                delay(2500)
                simulateSuccessfulConnection(WalletType.THIRDWEB_EMBEDDED, "******************************************")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error creating thirdweb embedded wallet", e)
            _connectionStatus.value = "Failed to create embedded wallet"
        }
    }
    
    /**
     * Disconnect the current wallet
     */
    fun disconnect() {
        try {
            _connectionStatus.value = "Disconnecting..."
            
            scope.launch {
                delay(1000)
                _isConnected.value = false
                _walletAddress.value = null
                _walletType.value = null
                _balance.value = "0.0"
                _connectionStatus.value = "Disconnected"
                Log.d(TAG, "Wallet disconnected successfully")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error disconnecting wallet", e)
            _connectionStatus.value = "Error disconnecting"
        }
    }
    
    /**
     * Get wallet balance (simulated for demo)
     */
    fun refreshBalance() {
        if (_isConnected.value == true) {
            scope.launch {
                try {
                    _connectionStatus.value = "Refreshing balance..."
                    delay(1500)
                    
                    // Simulate fetching balance
                    val simulatedBalance = (Math.random() * 10).toString().take(6)
                    _balance.value = "$simulatedBalance ETH"
                    _connectionStatus.value = "Connected"
                } catch (e: Exception) {
                    Log.e(TAG, "Error refreshing balance", e)
                    _connectionStatus.value = "Error refreshing balance"
                }
            }
        }
    }
    
    private fun isMetaMaskInstalled(): Boolean {
        return try {
            context.packageManager.getPackageInfo(METAMASK_PACKAGE, 0)
            true
        } catch (e: Exception) {
            false
        }
    }
    
    private fun redirectToMetaMaskInstall() {
        try {
            val intent = Intent(Intent.ACTION_VIEW).apply {
                data = Uri.parse("https://play.google.com/store/apps/details?id=$METAMASK_PACKAGE")
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)
            _connectionStatus.value = "Please install MetaMask and try again"
        } catch (e: Exception) {
            Log.e(TAG, "Error redirecting to MetaMask install", e)
            _connectionStatus.value = "Unable to open Play Store"
        }
    }
    
    private fun simulateSuccessfulConnection(type: WalletType, address: String) {
        _isConnected.value = true
        _walletAddress.value = address
        _walletType.value = type
        _connectionStatus.value = "Connected"
        
        // Simulate getting balance
        refreshBalance()
        
        Log.d(TAG, "Successfully connected to ${type.name} wallet: $address")
    }
    
    fun cleanup() {
        scope.cancel()
    }
}
