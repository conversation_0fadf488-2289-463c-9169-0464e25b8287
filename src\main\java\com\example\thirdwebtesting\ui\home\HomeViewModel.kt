package com.example.thirdwebtesting.ui.home

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel

class HomeViewModel : ViewModel() {

    private val _text = MutableLiveData<String>().apply {
        value = "Welcome to Reown AppKit Testing App!\n\nThis app demonstrates wallet connectivity using Reown AppKit (formerly WalletConnect). Navigate to the Wallet section to connect your Web3 wallet and start exploring blockchain functionality."
    }
    val text: LiveData<String> = _text
}