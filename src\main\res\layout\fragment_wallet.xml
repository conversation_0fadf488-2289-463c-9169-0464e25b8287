<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:gravity="center"
    tools:context=".ui.wallet.WalletFragment">

    <!-- Header -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Reown AppKit Integration"
        android:textSize="28sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="16dp"
        android:textColor="@color/purple_500" />

    <!-- Description -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Click the button below to open the complete Reown AppKit wallet connection interface. AppKit provides all the UI and functionality for connecting to various wallets."
        android:textSize="16sp"
        android:gravity="center"
        android:layout_marginBottom="32dp"
        android:textColor="@android:color/black" />

    <!-- Connect Wallet Button -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_connect_wallet"
        android:layout_width="match_parent"
        android:layout_height="64dp"
        android:text="Open AppKit Wallet Modal"
        android:textSize="18sp"
        android:layout_marginBottom="24dp"
        app:icon="@android:drawable/ic_secure"
        app:iconGravity="start"
        app:cornerRadius="12dp"
        style="@style/Widget.Material3.Button" />

    <!-- Info Text -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="AppKit handles all wallet connections, UI, and state management automatically. No custom UI needed!"
        android:textSize="14sp"
        android:textStyle="italic"
        android:gravity="center"
        android:layout_marginTop="16dp"
        android:textColor="@android:color/darker_gray" />

</LinearLayout>
