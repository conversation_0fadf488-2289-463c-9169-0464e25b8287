<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    tools:context=".ui.wallet.WalletFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Header -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Reown AppKit Wallet Connection"
            android:textSize="24sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="24dp"
            android:textColor="@color/purple_500" />

        <!-- Connection Status -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Connection Status"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tv_connection_status"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Disconnected"
                    android:textSize="16sp"
                    android:textColor="@android:color/black" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Connection Buttons Layout -->
        <LinearLayout
            android:id="@+id/layout_connection_buttons"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="24dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Choose a wallet to connect:"
                android:textSize="16sp"
                android:layout_marginBottom="16dp"
                android:gravity="center" />

            <!-- MetaMask Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_connect_metamask"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="Connect MetaMask"
                android:textSize="16sp"
                android:layout_marginBottom="12dp"
                app:icon="@android:drawable/ic_secure"
                app:iconGravity="start"
                style="@style/Widget.Material3.Button.OutlinedButton" />

            <!-- WalletConnect Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_connect_walletconnect"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="Connect via WalletConnect"
                android:textSize="16sp"
                android:layout_marginBottom="12dp"
                app:icon="@android:drawable/ic_menu_share"
                app:iconGravity="start"
                style="@style/Widget.Material3.Button.OutlinedButton" />

            <!-- thirdweb Embedded Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_connect_thirdweb"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="Create Embedded Wallet"
                android:textSize="16sp"
                android:layout_marginBottom="12dp"
                app:icon="@android:drawable/ic_menu_add"
                app:iconGravity="start"
                style="@style/Widget.Material3.Button" />

        </LinearLayout>

        <!-- Wallet Info Layout (Hidden by default) -->
        <androidx.cardview.widget.CardView
            android:id="@+id/layout_wallet_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:visibility="gone"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Wallet Information"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="12dp" />

                <TextView
                    android:id="@+id/tv_wallet_type"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="No wallet connected"
                    android:textSize="14sp"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tv_wallet_address"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="No wallet connected"
                    android:textSize="14sp"
                    android:layout_marginBottom="8dp"
                    android:fontFamily="monospace" />

                <TextView
                    android:id="@+id/tv_balance"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Balance: 0.0 ETH"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/purple_500" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Action Buttons (Hidden by default) -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_refresh_balance"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Refresh Balance"
                android:layout_marginEnd="8dp"
                android:visibility="gone"
                style="@style/Widget.Material3.Button.OutlinedButton" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_disconnect"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Disconnect"
                android:layout_marginStart="8dp"
                android:visibility="gone"
                android:backgroundTint="@android:color/holo_red_dark"
                style="@style/Widget.Material3.Button" />

        </LinearLayout>

        <!-- Info Text -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="This is a demo implementation of Reown AppKit wallet connectivity. In a production app, you would integrate with the actual Reown AppKit SDK and handle real wallet connections."
            android:textSize="12sp"
            android:textStyle="italic"
            android:gravity="center"
            android:layout_marginTop="24dp"
            android:textColor="@android:color/darker_gray" />

    </LinearLayout>

</ScrollView>
