package com.example.thirdwebtesting.ui.home

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.example.thirdwebtesting.databinding.FragmentHomeBinding
import com.reown.appkit.client.AppKit
import com.reown.appkit.client.models.Session

class HomeFragment : Fragment() {

    private var _binding: FragmentHomeBinding? = null


    // This property is only valid between onCreateView and
    // onDestroyView.
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val homeViewModel =
            ViewModelProvider(this).get(HomeViewModel::class.java)

        _binding = FragmentHomeBinding.inflate(inflater, container, false)
        val root: View = binding.root

        val textView: TextView = binding.textHome
        homeViewModel.text.observe(viewLifecycleOwner) {
            textView.text = it
        }

        // Observe AppKit session state
        AppKit.getSessionState().observe(viewLifecycleOwner) { sessionState ->
            val statusText = when (sessionState) {
                is Session.State.Connected -> "✅ Wallet Connected"
                is Session.State.Disconnected -> "❌ No Wallet Connected"
            }
            // You can add a status TextView to the home layout if needed
        }

        return root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}