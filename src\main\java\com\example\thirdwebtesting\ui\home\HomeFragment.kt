package com.example.thirdwebtesting.ui.home

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.example.thirdwebtesting.databinding.FragmentHomeBinding
import com.example.thirdwebtesting.wallet.WalletManager

class HomeFragment : Fragment() {

    private var _binding: FragmentHomeBinding? = null
    private lateinit var walletManager: WalletManager

    // This property is only valid between onCreateView and
    // onDestroyView.
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val homeViewModel =
            ViewModelProvider(this).get(HomeViewModel::class.java)

        walletManager = WalletManager.getInstance(requireContext())

        _binding = FragmentHomeBinding.inflate(inflater, container, false)
        val root: View = binding.root

        val textView: TextView = binding.textHome
        homeViewModel.text.observe(viewLifecycleOwner) {
            textView.text = it
        }

        // Observe wallet connection status
        walletManager.isConnected.observe(viewLifecycleOwner) { isConnected ->
            val statusText = if (isConnected) {
                "✅ Wallet Connected"
            } else {
                "❌ No Wallet Connected"
            }
            // You can add a status TextView to the home layout if needed
        }

        return root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}