package com.example.thirdwebtesting

import android.app.Application
import com.reown.appkit.client.AppKit
import com.reown.appkit.client.models.AppMetaData

class ThirdwebTestingApplication : Application() {

    override fun onCreate() {
        super.onCreate()
        
        // Initialize Reown AppKit
        val projectId = "YOUR_PROJECT_ID" // Get this from https://cloud.reown.com
        val appMetaData = AppMetaData(
            name = "Thirdweb Testing App",
            description = "A demo app showcasing wallet connectivity",
            url = "https://example.com",
            icons = listOf("https://example.com/icon.png"),
            redirect = "kotlin-dapp-wc:/request" // Custom redirect for your app
        )

        AppKit.initialize(
            application = this,
            projectId = projectId,
            appMetaData = appMetaData
        ) { error ->
            // Handle initialization error
            error?.let {
                println("AppKit initialization error: ${it.throwable.message}")
            }
        }
    }
}
