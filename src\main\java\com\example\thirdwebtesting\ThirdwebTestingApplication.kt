package com.example.thirdwebtesting

import android.app.Application
import com.reown.appkit.client.AppKit
import com.reown.appkit.client.Modal
import com.reown.appkit.presets.AppKitChainsPresets
import com.reown.core.client.CoreClient
import com.reown.core.model.ConnectionType
import com.reown.core.model.Core

class ThirdwebTestingApplication : Application() {

    override fun onCreate() {
        super.onCreate()

        // Initialize Reown AppKit with your project ID
        val projectId = "7b44092789f3f1fd094a8bbec200372b"
        val connectionType = ConnectionType.AUTOMATIC
        val appMetaData = Core.Model.AppMetaData(
            name = "Thirdweb Testing App",
            description = "A demo app showcasing wallet connectivity with Reown AppKit",
            url = "https://example.com",
            icons = listOf("https://example.com/icon.png"),
            redirect = "kotlin-modal-wc://request"
        )

        try {
            // Initialize CoreClient first
            CoreClient.initialize(
                projectId = projectId,
                connectionType = connectionType,
                application = this,
                metaData = appMetaData
            )

            // Initialize AppKit
            AppKit.initialize(
                init = Modal.Params.Init(CoreClient),
                onSuccess = {
                    println("AppKit initialized successfully")
                    // Set supported chains
                    AppKit.setChains(AppKitChainsPresets.ethChains.values.toList())
                },
                onError = { error ->
                    println("AppKit initialization error: ${error.throwable.message}")
                }
            )
        } catch (e: Exception) {
            println("Failed to initialize AppKit: ${e.message}")
        }
    }
}
