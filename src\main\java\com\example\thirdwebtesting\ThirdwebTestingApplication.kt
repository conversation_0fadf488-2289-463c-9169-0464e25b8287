package com.example.thirdwebtesting

import android.app.Application
import com.reown.android.Core
import com.reown.android.CoreClient
import com.reown.appkit.client.AppKit
import com.reown.appkit.client.Modal

class ThirdwebTestingApplication : Application() {

    override fun onCreate() {
        super.onCreate()

        // Initialize Reown AppKit with your project ID
        val projectId = "7b44092789f3f1fd094a8bbec200372b"

        // App metadata
        val appMetaData = Core.Model.AppMetaData(
            name = "Thirdweb Testing App",
            description = "Demo app with Reown AppKit wallet connectivity",
            url = "https://example.com",
            icons = listOf("https://example.com/icon.png"),
            redirect = "kotlin-appkit://request"
        )

        try {
            // Initialize Core Client
            CoreClient.initialize(
                projectId = projectId,
                connectionType = Core.Model.ConnectionType.AUTOMATIC,
                application = this,
                metaData = appMetaData
            )

            // Initialize AppKit
            AppKit.initialize(
                init = Modal.Params.Init(CoreClient),
                onSuccess = {
                    println("✅ AppKit initialized successfully!")
                },
                onError = { error ->
                    println("❌ AppKit initialization failed: ${error.throwable.message}")
                }
            )
        } catch (e: Exception) {
            println("❌ Failed to initialize AppKit: ${e.message}")
        }
    }
}
